import logging
import threading
from datetime import date, timedelta

from flask import Flask, jsonify

from aws import turn_off_instance
from environment import get_aws_config, get_db_config
from database_task_executor import DatabaseTask, execute_database_tasks
from environment import setup_environment

app = Flask(__name__)
logging.getLogger('werkzeug').setLevel(logging.ERROR)


def run_daily_views_and_shutdown():

    current_date = date.today()
    current_date_str = current_date.strftime('%Y_%m')
    seven_days_before_str = (current_date - timedelta(weeks=1)).strftime('%Y_%m')


    task_definitions = {
        'current': [
            DatabaseTask('refresh_materialize_view_concurrently', f'effective_rent_by_{current_date_str}'),
            DatabaseTask('refresh_materialize_view_concurrently', f'rent_listing_by_{current_date_str}'),
            DatabaseTask('refresh_materialize_view_concurrently_without_date_of_record', 'bedroom_future_availability')

        ],
        'previous': [
            DatabaseTask('refresh_materialize_view_concurrently', f'effective_rent_by_{seven_days_before_str}'),
            DatabaseTask('refresh_materialize_view_concurrently', f'rent_listing_by_{seven_days_before_str}'),
        ]
    }

    setup_environment()
    db_config = get_db_config()

    execute_database_tasks(task_definitions['current'], db_config)

    if seven_days_before_str != current_date_str:
        execute_database_tasks(task_definitions['previous'], db_config)

    shutdown_instance()


def shutdown_instance():
    aws_config = get_aws_config()
    logger.info("Turning off instance...")
    turn_off_instance(**aws_config)


def setup_logging(quiet=False):
    """Configure logging based on mode"""
    level = logging.ERROR if quiet else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


# Configure global logger
logger = setup_logging()


@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "ok"}), 200


if __name__ == "__main__":
    task_thread = threading.Thread(target=run_daily_views_and_shutdown)
    task_thread.daemon = True
    task_thread.start()

    app.run(host='0.0.0.0', port=8080, debug=False)