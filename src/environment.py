import logging
import os
from dotenv import load_dotenv


def setup_environment():
    env_file = os.path.join(os.path.dirname(__file__), '.env')

    logger = logging.getLogger(__name__)

    # this is for local environment
    if os.path.exists(env_file):

        load_dotenv(env_file)
        logger.info("Loaded configuration from .env file")
    else:
        logger.info("Using system environment variables")


def get_db_config():
    db_config = {
        'url': os.getenv("POSTGRES_DB_URL"),
        'user': os.getenv("POSTGRES_DB_USER"),
        'password': os.getenv("POSTGRES_DB_PASSWORD")
    }
    return db_config


def get_aws_config():
    aws_config = {
        'service_name': os.getenv("DD_SERVICE") + "-service",
        'cluster_name': os.getenv("DD_ENV"),
        'region_name':  os.getenv("AWS_REGION")
    }
    return aws_config