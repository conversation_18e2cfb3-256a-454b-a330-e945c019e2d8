import logging
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from urllib.parse import urlparse


# Crear logger específico para esta clase
logger = logging.getLogger(__name__)

class PostgreSQLProcedureRunner:
    def __init__(self, url, user, password):
        self.jdbc_url = url
        self.user = user
        self.password = password


    def connect(self):
        """Establish connection with PostgreSQL"""
        try:
            postgres_url = self.jdbc_url.replace('jdbc:', '')
            parsed = urlparse(postgres_url)

            self.connection = psycopg2.connect(
                host=parsed.hostname,
                database=parsed.path.lstrip('/'),
                user=self.user,
                password=self.password,
                port=parsed.port
            )

            # Configure to capture RAISE NOTICE
            self.connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)

            # Configure message level to capture NOTICE
            cursor = self.connection.cursor()
            cursor.execute("SET client_min_messages TO NOTICE")
            cursor.close()

            logger.info("Connection established successfully with PostgreSQL")
            return True

        except Exception as e:
            logger.error(f"Error connecting to PostgreSQL: {e}")
            logger.error(f"Connection details: host={self.host}, database={self.database}, user={self.user}, port={self.port}")
            return False

    def show_notices(self):
        """Show RAISE NOTICE messages from PostgreSQL"""
        if self.connection and self.connection.notices:
            for notice in self.connection.notices:
                # Clean message from unnecessary prefixes
                clean_notice = notice.strip()
                if clean_notice.startswith('NOTICE:'):
                    clean_notice = clean_notice[7:].strip()
                logger.info(f"{clean_notice}")

            # Clear notices list after showing them
            self.connection.notices.clear()

    def call_procedure(self, procedure_name, params=None, show_output=True):
        """Execute stored procedure in PostgreSQL"""
        if not self.connection:
            logger.error("No connection established")
            return None

        try:
            # Clear previous notices
            self.connection.notices.clear()

            cursor = self.connection.cursor()

            if params:
                # With parameters
                placeholders = ', '.join(['%s' for _ in params])
                query = f"CALL {procedure_name}({placeholders})"
                cursor.execute(query, params)
            else:
                # Without parameters
                cursor.execute(f"CALL {procedure_name}()")

            # Show RAISE NOTICE messages first
            if show_output:
                self.show_notices()

            # Get results if any
            try:
                results = cursor.fetchall()

                if results and show_output:
                    logger.info(f"PROCEDURE RESULTS: {procedure_name}")

                    # Get column names if available
                    if cursor.description:
                        column_names = [desc[0] for desc in cursor.description]
                        logger.info(f"Columns: {' | '.join(column_names)}")
                        logger.info("-" * 60)

                        # Show data in tabular format
                        for i, row in enumerate(results, 1):
                            logger.info(f"Row {i}: {dict(zip(column_names, row))}")
                    else:
                        # No column description, show data directly
                        for i, row in enumerate(results, 1):
                            logger.info(f"Row {i}: {row}")


                return results

            except psycopg2.ProgrammingError:
                # Procedure may not return results, but may have notices
                return []

        except Exception as e:
            logger.error(f"Error executing procedure: {e}")
            # Still show notices if any
            if show_output:
                self.show_notices()
            return None
        finally:
            cursor.close()

    def close_connection(self):
        """Close connection"""
        if self.connection:
            self.connection.close()
            logger.info("Connection closed")