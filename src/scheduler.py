import logging


def execute_procedure_with_connection(db_runner, procedure_name, params, show_output=True):

    logger = logging.getLogger(__name__)
    try:
        if not db_runner.connect():
            logger.error(f"Could not establish connection for {procedure_name}")
            return None

        logger.info(f" Executing procedure: {procedure_name} with param: {params}")
        results = db_runner.call_procedure(procedure_name, [params], show_output)

        if results is not None:
            logger.info(f"[{procedure_name} with param: {params}] Completed successfully")
            return results
        else:
            logger.error(f"[{procedure_name} with param: {params}] Error in execution")
            return None

    except Exception as e:
        logger.error(f"[{procedure_name} with param: {params}] Error: {e}")
        return None
    finally:
        db_runner.close_connection()
